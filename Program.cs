// HTTP Server - Stage 8: Read Request Body (POST /files/{filename})
// Serves static files and accepts POST requests to create files with request body parsing

using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;

// A record to cleanly hold the parsed request data.
public record HttpRequest(
    string Method,
    string Path,
    Dictionary<string, string> Headers,
    string Body
);

class Program
{
    static async Task Main(string[] args)
    {
        // Parse command-line arguments to get the directory
        string directory = "."; // Default to current directory
        for (int i = 0; i < args.Length; i++)
        {
            if (args[i] == "--directory" && i + 1 < args.Length)
            {
                directory = args[i + 1];
                break;
            }
        }
        Console.WriteLine($"Serving files from directory: {Path.GetFullPath(directory)}");

        IPAddress ipAddress = IPAddress.Loopback;
        int port = 4221;
        TcpListener server = new TcpListener(ipAddress, port);

        try
        {
            server.Start();
            Console.WriteLine($"Server started. Listening on {ipAddress}:{port}");

            // Main server loop - accepts connections continuously
            while (true)
            {
                // Accept new client connection asynchronously
                TcpClient client = await server.AcceptTcpClientAsync();
                Console.WriteLine("Client connected.");

                // CONCURRENT HANDLING: Process each client on a separate thread
                // This allows the server to immediately accept the next connection
                // while the current one is being handled in the background
                // Pass the directory to the client handler
                _ = Task.Run(() => HandleClient(client, directory));
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"An error occurred: {ex.Message}");
        }
        finally
        {
            server.Stop();
        }
    }

    /// <summary>
    /// Handles individual client connections asynchronously.
    /// Each client is processed independently on a separate thread.
    /// </summary>
    private static async Task HandleClient(TcpClient client, string directory)
    {
        try
        {
            await using NetworkStream stream = client.GetStream();

            // Read the full request, including headers and body.
            HttpRequest request = await ParseRequestAsync(stream);
            Console.WriteLine($"Received {request.Method} request for {request.Path}");

            byte[] responseBytes;

            if (request.Path.StartsWith("/files/"))
            {
                string filename = request.Path.Substring("/files/".Length);
                string filePath = Path.Combine(directory, filename);

                if (request.Method == "GET")
                {
                    if (File.Exists(filePath))
                    {
                        Console.WriteLine($"Serving file: {filePath}");
                        byte[] fileBytes = await File.ReadAllBytesAsync(filePath);
                        string responseHeaders = $"HTTP/1.1 200 OK\r\nContent-Type: application/octet-stream\r\nContent-Length: {fileBytes.Length}\r\n\r\n";
                        byte[] headerBytes = Encoding.ASCII.GetBytes(responseHeaders);
                        await stream.WriteAsync(headerBytes);
                        await stream.WriteAsync(fileBytes);
                        return; // Exit after sending response
                    }
                    else
                    {
                        Console.WriteLine($"File not found: {filePath}");
                        responseBytes = Encoding.ASCII.GetBytes("HTTP/1.1 404 Not Found\r\n\r\n");
                    }
                }
                else if (request.Method == "POST")
                {
                    Console.WriteLine($"Creating file: {filePath}");
                    await File.WriteAllTextAsync(filePath, request.Body);
                    responseBytes = Encoding.ASCII.GetBytes("HTTP/1.1 201 Created\r\n\r\n");
                }
                else
                {
                    responseBytes = Encoding.ASCII.GetBytes("HTTP/1.1 405 Method Not Allowed\r\n\r\n");
                }
            }
            else if (request.Path.StartsWith("/echo/"))
            {
                string content = request.Path.Substring("/echo/".Length);
                responseBytes = Encoding.ASCII.GetBytes($"HTTP/1.1 200 OK\r\nContent-Type: text/plain\r\nContent-Length: {content.Length}\r\n\r\n{content}");
            }
            else if (request.Path == "/user-agent")
            {
                string userAgent = request.Headers.GetValueOrDefault("User-Agent", "Unknown");
                responseBytes = Encoding.ASCII.GetBytes($"HTTP/1.1 200 OK\r\nContent-Type: text/plain\r\nContent-Length: {userAgent.Length}\r\n\r\n{userAgent}");
            }
            else if (request.Path == "/")
            {
                responseBytes = Encoding.ASCII.GetBytes("HTTP/1.1 200 OK\r\n\r\n");
            }
            else
            {
                responseBytes = Encoding.ASCII.GetBytes("HTTP/1.1 404 Not Found\r\n\r\n");
            }

            await stream.WriteAsync(responseBytes);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error handling client: {ex.Message}");
        }
        finally
        {
            client.Close();
            Console.WriteLine("Client disconnected.");
        }
    }

    private static async Task<HttpRequest> ParseRequestAsync(NetworkStream stream)
    {
        // This parser is simplified for the challenge and assumes a well-behaved client.
        byte[] buffer = new byte[2048];
        int bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length);
        string requestData = Encoding.ASCII.GetString(buffer, 0, bytesRead);

        string[] parts = requestData.Split("\r\n\r\n", 2);
        string headerBlock = parts[0];
        string body = parts.Length > 1 ? parts[1] : string.Empty;

        string[] headerLines = headerBlock.Split("\r\n");
        string[] requestLineParts = headerLines[0].Split(' ');
        string method = requestLineParts[0];
        string path = requestLineParts[1];

        var headers = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
        for (int i = 1; i < headerLines.Length; i++)
        {
            string headerLine = headerLines[i];
            int separatorIndex = headerLine.IndexOf(": ");
            if (separatorIndex > 0)
            {
                headers[headerLine.Substring(0, separatorIndex)] = headerLine.Substring(separatorIndex + 2);
            }
        }

        // The initial read might not have captured the full body if it's large.
        // This logic handles reading the rest of the body based on Content-Length.
        if (headers.TryGetValue("Content-Length", out var contentLengthStr) && int.TryParse(contentLengthStr, out int contentLength))
        {
            int bodyBytesReceived = Encoding.ASCII.GetByteCount(body);
            if (bodyBytesReceived < contentLength)
            {
                byte[] bodyBuffer = new byte[contentLength];
                int bodyOffset = Encoding.ASCII.GetBytes(body, bodyBuffer);

                while (bodyOffset < contentLength)
                {
                    bytesRead = await stream.ReadAsync(bodyBuffer, bodyOffset, contentLength - bodyOffset);
                    if (bytesRead == 0) break;
                    bodyOffset += bytesRead;
                }
                body = Encoding.ASCII.GetString(bodyBuffer, 0, bodyOffset);
            }
        }

        return new HttpRequest(method, path, headers, body);
    }
}
